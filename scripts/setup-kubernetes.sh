#!/bin/bash

# Kubernetes环境检查和设置脚本
# 用于ESS-HELM部署包

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 日志函数
info() {
    echo -e "${CYAN}[信息]${NC} $1"
}

success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 检查Kubernetes环境
check_kubernetes_environment() {
    info "检查Kubernetes环境..."
    
    # 检查kubectl是否安装
    if ! command -v kubectl &> /dev/null; then
        error "kubectl未安装"
        return 1
    fi
    
    success "kubectl已安装: $(kubectl version --client --short 2>/dev/null || echo '版本未知')"
    
    # 检查kubeconfig
    local kubeconfig_found=false
    
    if [[ -n "$KUBECONFIG" ]]; then
        if [[ -f "$KUBECONFIG" ]]; then
            info "找到KUBECONFIG环境变量: $KUBECONFIG"
            kubeconfig_found=true
        else
            warning "KUBECONFIG环境变量指向不存在的文件: $KUBECONFIG"
        fi
    fi
    
    if [[ -f "$HOME/.kube/config" ]]; then
        info "找到默认kubeconfig: $HOME/.kube/config"
        kubeconfig_found=true
    fi
    
    if [[ "$kubeconfig_found" == "false" ]]; then
        warning "未找到kubeconfig文件"
        return 1
    fi
    
    # 测试集群连接
    info "测试Kubernetes集群连接..."
    if kubectl cluster-info &>/dev/null; then
        success "Kubernetes集群连接正常"
        kubectl cluster-info
        return 0
    else
        error "无法连接到Kubernetes集群"
        return 1
    fi
}

# 安装k3s (轻量级Kubernetes)
install_k3s() {
    info "开始安装k3s..."
    
    # 检查是否已安装
    if command -v k3s &> /dev/null; then
        warning "k3s已安装，跳过安装步骤"
        return 0
    fi
    
    # 下载并安装k3s
    info "下载k3s安装脚本..."
    curl -sfL https://get.k3s.io | sh -
    
    if [[ $? -eq 0 ]]; then
        success "k3s安装成功"
        
        # 设置kubeconfig
        info "配置kubeconfig..."
        mkdir -p "$HOME/.kube"
        sudo cp /etc/rancher/k3s/k3s.yaml "$HOME/.kube/config"
        sudo chown $(id -u):$(id -g) "$HOME/.kube/config"
        
        # 等待k3s启动
        info "等待k3s启动..."
        sleep 10
        
        # 验证安装
        if kubectl cluster-info &>/dev/null; then
            success "k3s安装并配置成功"
            return 0
        else
            error "k3s安装后无法连接集群"
            return 1
        fi
    else
        error "k3s安装失败"
        return 1
    fi
}

# 安装microk8s
install_microk8s() {
    info "开始安装microk8s..."
    
    # 检查是否已安装
    if command -v microk8s &> /dev/null; then
        warning "microk8s已安装，跳过安装步骤"
        return 0
    fi
    
    # 安装microk8s
    info "安装microk8s..."
    sudo snap install microk8s --classic
    
    if [[ $? -eq 0 ]]; then
        success "microk8s安装成功"
        
        # 添加用户到microk8s组
        sudo usermod -a -G microk8s $USER
        sudo chown -f -R $USER ~/.kube
        
        # 等待microk8s启动
        info "等待microk8s启动..."
        sudo microk8s status --wait-ready
        
        # 设置kubeconfig
        info "配置kubeconfig..."
        mkdir -p "$HOME/.kube"
        sudo microk8s config > "$HOME/.kube/config"
        
        # 启用必要的插件
        info "启用必要的插件..."
        sudo microk8s enable dns storage
        
        # 验证安装
        if microk8s kubectl cluster-info &>/dev/null; then
            success "microk8s安装并配置成功"
            
            # 创建kubectl别名
            echo "alias kubectl='microk8s kubectl'" >> ~/.bashrc
            
            return 0
        else
            error "microk8s安装后无法连接集群"
            return 1
        fi
    else
        error "microk8s安装失败"
        return 1
    fi
}

# 主菜单
show_menu() {
    echo
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                  Kubernetes环境设置                          ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo
    echo "请选择操作:"
    echo "1. 检查当前Kubernetes环境"
    echo "2. 安装k3s (推荐，轻量级)"
    echo "3. 安装microk8s (Ubuntu推荐)"
    echo "4. 显示详细诊断信息"
    echo "0. 退出"
    echo
    echo -e "${YELLOW}请选择 (0-4):${NC}"
}

# 显示详细诊断信息
show_diagnostic_info() {
    echo
    echo -e "${CYAN}=== Kubernetes环境诊断 ===${NC}"
    echo
    
    # kubectl版本
    echo -e "${YELLOW}kubectl版本:${NC}"
    kubectl version --client 2>/dev/null || echo "kubectl未安装"
    echo
    
    # kubeconfig文件
    echo -e "${YELLOW}kubeconfig文件:${NC}"
    if [[ -n "$KUBECONFIG" ]]; then
        echo "KUBECONFIG环境变量: $KUBECONFIG"
        [[ -f "$KUBECONFIG" ]] && echo "文件存在: 是" || echo "文件存在: 否"
    else
        echo "KUBECONFIG环境变量: 未设置"
    fi
    
    if [[ -f "$HOME/.kube/config" ]]; then
        echo "默认kubeconfig: $HOME/.kube/config (存在)"
    else
        echo "默认kubeconfig: $HOME/.kube/config (不存在)"
    fi
    echo
    
    # 集群连接测试
    echo -e "${YELLOW}集群连接测试:${NC}"
    kubectl cluster-info 2>&1 || echo "无法连接到集群"
    echo
    
    # 系统信息
    echo -e "${YELLOW}系统信息:${NC}"
    echo "操作系统: $(uname -s)"
    echo "架构: $(uname -m)"
    echo "发行版: $(lsb_release -d 2>/dev/null | cut -f2 || echo '未知')"
    echo
    
    # 已安装的Kubernetes工具
    echo -e "${YELLOW}已安装的Kubernetes工具:${NC}"
    command -v k3s &>/dev/null && echo "k3s: $(k3s --version 2>/dev/null | head -1)" || echo "k3s: 未安装"
    command -v microk8s &>/dev/null && echo "microk8s: $(microk8s version 2>/dev/null | head -1)" || echo "microk8s: 未安装"
    command -v docker &>/dev/null && echo "docker: $(docker --version 2>/dev/null)" || echo "docker: 未安装"
    echo
}

# 主程序
main() {
    while true; do
        show_menu
        read -r choice
        
        case "$choice" in
            1)
                check_kubernetes_environment
                echo -e "${YELLOW}按任意键继续...${NC}"
                read -r
                ;;
            2)
                install_k3s
                echo -e "${YELLOW}按任意键继续...${NC}"
                read -r
                ;;
            3)
                install_microk8s
                echo -e "${YELLOW}按任意键继续...${NC}"
                read -r
                ;;
            4)
                show_diagnostic_info
                echo -e "${YELLOW}按任意键继续...${NC}"
                read -r
                ;;
            0)
                info "退出Kubernetes环境设置"
                exit 0
                ;;
            *)
                error "无效选择，请重新输入"
                ;;
        esac
    done
}

# 如果直接运行脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
