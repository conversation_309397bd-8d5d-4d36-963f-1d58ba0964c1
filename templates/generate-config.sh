#!/bin/bash
# ESS-HELM Matrix服务配置生成脚本
# 版本: 25.6.2
# 用途: 基于变量模板生成实际的Helm values配置文件

set -euo pipefail

# 脚本配置
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly TEMPLATE_FILE="${SCRIPT_DIR}/values-template.yaml"
readonly CONFIG_FILE="${SCRIPT_DIR}/config-variables.env"
readonly OUTPUT_FILE="${SCRIPT_DIR}/../values.yaml"

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖工具..."
    
    if ! command -v envsubst &> /dev/null; then
        log_error "envsubst 未安装，请安装 gettext 包"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 检查文件存在性
check_files() {
    log_info "检查模板文件..."
    
    if [[ ! -f "$TEMPLATE_FILE" ]]; then
        log_error "模板文件不存在: $TEMPLATE_FILE"
        exit 1
    fi
    
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_error "配置文件不存在: $CONFIG_FILE"
        log_info "请复制 config-variables.env 并修改配置"
        exit 1
    fi
    
    log_success "文件检查完成"
}

# 加载配置变量
load_config() {
    log_info "加载配置变量..."
    
    # 导出所有变量以供envsubst使用
    set -a
    source "$CONFIG_FILE"
    set +a
    
    log_success "配置变量加载完成"
}

# 验证必需变量
validate_config() {
    log_info "验证配置变量..."

    local required_vars=(
        "MAIN_DOMAIN"
        "ELEMENT_SUBDOMAIN"
        "MATRIX_SUBDOMAIN"
        "AUTH_SUBDOMAIN"
        "RTC_SUBDOMAIN"
        "INGRESS_CLASS"
        "CERT_METHOD"
    )

    local missing_vars=()

    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            missing_vars+=("$var")
        fi
    done

    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log_error "缺少必需的配置变量:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        exit 1
    fi

    # 验证域名格式
    if [[ ! "$MAIN_DOMAIN" =~ ^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\.[a-zA-Z]{2,}$ ]]; then
        log_error "主域名格式无效: $MAIN_DOMAIN"
        exit 1
    fi

    # 验证Ingress Controller类型
    if [[ "$INGRESS_CLASS" != "nginx" && "$INGRESS_CLASS" != "traefik" ]]; then
        log_error "不支持的Ingress Controller类型: $INGRESS_CLASS"
        log_info "支持的类型: nginx, traefik"
        exit 1
    fi

    # 验证证书申请方式
    local valid_cert_methods=("letsencrypt-http01" "letsencrypt-dns01" "letsencrypt-dns01-staging")
    local cert_method_valid=false
    for method in "${valid_cert_methods[@]}"; do
        if [[ "$CERT_METHOD" == "$method" ]]; then
            cert_method_valid=true
            break
        fi
    done

    if [[ "$cert_method_valid" != "true" ]]; then
        log_error "不支持的证书申请方式: $CERT_METHOD"
        log_info "支持的方式: ${valid_cert_methods[*]}"
        exit 1
    fi

    # 验证DNS验证相关配置
    if [[ "$CERT_METHOD" == *"dns01"* ]]; then
        if [[ -z "${DNS_PROVIDER:-}" ]]; then
            log_error "DNS验证方式需要配置 DNS_PROVIDER"
            exit 1
        fi
        if [[ -z "${DNS_API_TOKEN:-}" ]]; then
            log_error "DNS验证方式需要配置 DNS_API_TOKEN"
            exit 1
        fi
    fi

    log_success "配置验证完成"
}

# 设置证书颁发者
setup_cert_issuer() {
    log_info "配置证书颁发者..."

    case "$CERT_METHOD" in
        "letsencrypt-http01")
            export CERT_ISSUER="letsencrypt-http01"
            log_info "使用HTTP验证申请生产证书"
            ;;
        "letsencrypt-dns01")
            export CERT_ISSUER="letsencrypt-dns01-${DNS_PROVIDER}"
            log_info "使用DNS验证申请生产证书 (${DNS_PROVIDER})"
            ;;
        "letsencrypt-dns01-staging")
            export CERT_ISSUER="letsencrypt-dns01-staging-${DNS_PROVIDER}"
            log_info "使用DNS验证申请测试证书 (${DNS_PROVIDER})"
            ;;
    esac

    log_success "证书颁发者配置完成: $CERT_ISSUER"
}

# 生成配置文件
generate_config() {
    log_info "生成Helm values配置文件..."

    # 设置证书颁发者
    setup_cert_issuer

    # 使用envsubst替换模板中的变量
    envsubst < "$TEMPLATE_FILE" > "$OUTPUT_FILE"

    log_success "配置文件生成完成: $OUTPUT_FILE"
}

# 显示配置摘要
show_summary() {
    log_info "配置摘要:"
    echo "  主域名: $MAIN_DOMAIN"
    echo "  Element Web: ${ELEMENT_SUBDOMAIN}.${MAIN_DOMAIN}"
    echo "  Matrix服务器: ${MATRIX_SUBDOMAIN}.${MAIN_DOMAIN}"
    echo "  认证服务: ${AUTH_SUBDOMAIN}.${MAIN_DOMAIN}"
    echo "  RTC服务: ${RTC_SUBDOMAIN}.${MAIN_DOMAIN}"
    echo "  Ingress Controller: $INGRESS_CLASS"
    echo "  证书申请方式: $CERT_METHOD"
    echo "  证书颁发者: $CERT_ISSUER"
    echo "  证书邮箱: $CERT_EMAIL"
    if [[ "$CERT_METHOD" == *"dns01"* ]]; then
        echo "  DNS提供商: $DNS_PROVIDER"
    fi
    echo "  输出文件: $OUTPUT_FILE"
}

# 提供下一步指导
show_next_steps() {
    log_info "下一步操作:"
    echo "1. 验证配置文件:"
    echo "   helm upgrade --install matrix-stack oci://ghcr.io/element-hq/ess-helm/matrix-stack \\"
    echo "     --version 25.6.2 --namespace matrix --values $OUTPUT_FILE --dry-run"
    echo ""
    echo "2. 执行部署:"
    echo "   helm upgrade --install matrix-stack oci://ghcr.io/element-hq/ess-helm/matrix-stack \\"
    echo "     --version 25.6.2 --namespace matrix --values $OUTPUT_FILE"
    echo ""
    echo "3. 检查部署状态:"
    echo "   kubectl get pods -n matrix"
    echo "   kubectl get ingress -n matrix"
}

# 主函数
main() {
    log_info "ESS-HELM Matrix服务配置生成器 v25.6.2"
    echo ""
    
    check_dependencies
    check_files
    load_config
    validate_config
    generate_config
    
    echo ""
    show_summary
    echo ""
    show_next_steps
    
    log_success "配置生成完成！"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
