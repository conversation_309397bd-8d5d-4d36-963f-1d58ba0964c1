# ESS-HELM Matrix服务配置文件模板
# 版本: 25.6.2 (基于Element官方ESS-HELM稳定版)
# 用途: 支持变量替换的通用配置模板
# 
# 使用方法:
# 1. 复制此模板文件
# 2. 替换所有 ${VARIABLE} 变量为实际值
# 3. 使用 helm --dry-run 验证配置正确性
# 4. 执行实际部署

# ⚠️ 重要: serverName必须使用主域名，不是子域名
# 这影响Matrix用户ID格式: @username:${MAIN_DOMAIN}
serverName: "${MAIN_DOMAIN}"

# 全局Ingress配置
ingress:
  className: "${INGRESS_CLASS}"  # 支持 "nginx" 或 "traefik"
  tlsEnabled: true
  annotations:
    # 根据Ingress Controller类型选择对应的注解
    # Nginx Ingress Controller注解
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    # Traefik Ingress Controller注解  
    traefik.ingress.kubernetes.io/router.tls: "true"

# Element Web客户端配置
elementWeb:
  enabled: true
  ingress:
    host: "${ELEMENT_SUBDOMAIN}.${MAIN_DOMAIN}"

# Synapse Matrix服务器配置
synapse:
  enabled: true
  ingress:
    host: "${MATRIX_SUBDOMAIN}.${MAIN_DOMAIN}"

# Matrix认证服务配置
matrixAuthenticationService:
  enabled: true
  ingress:
    host: "${AUTH_SUBDOMAIN}.${MAIN_DOMAIN}"

# Matrix RTC服务配置
matrixRTC:
  enabled: true
  ingress:
    host: "${RTC_SUBDOMAIN}.${MAIN_DOMAIN}"

# Well-known委托配置
# ⚠️ 注意: wellKnownDelegation组件不支持 ingress.host 属性
wellKnownDelegation:
  enabled: true

# 证书管理配置
certManager:
  clusterIssuer: "${CERT_ISSUER}"

# 可选: 自定义资源限制
# synapse:
#   resources:
#     requests:
#       memory: "1Gi"
#       cpu: "500m"
#     limits:
#       memory: "2Gi"
#       cpu: "1000m"

# 可选: 数据库配置 (使用内置PostgreSQL)
# postgres:
#   persistence:
#     size: "20Gi"
#     storageClass: "default"
