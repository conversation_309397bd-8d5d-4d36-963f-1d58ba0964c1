# ESS-HELM Matrix服务配置变量定义文件
# 版本: 25.6.2
# 用途: 定义所有可配置的变量，支持不同环境的部署

# =============================================================================
# 域名配置 (必须配置)
# =============================================================================

# 主域名 - Matrix服务器的主要标识
# 影响: 用户ID格式 @username:${MAIN_DOMAIN}
# 示例: example.com, matrix.org, your-domain.com
MAIN_DOMAIN="example.com"

# 子域名前缀配置
# Element Web客户端访问地址: ${ELEMENT_SUBDOMAIN}.${MAIN_DOMAIN}
ELEMENT_SUBDOMAIN="element"

# Matrix服务器访问地址: ${MATRIX_SUBDOMAIN}.${MAIN_DOMAIN}
MATRIX_SUBDOMAIN="matrix"

# 认证服务访问地址: ${AUTH_SUBDOMAIN}.${MAIN_DOMAIN}
AUTH_SUBDOMAIN="mas"

# RTC服务访问地址: ${RTC_SUBDOMAIN}.${MAIN_DOMAIN}
RTC_SUBDOMAIN="rtc"

# =============================================================================
# 技术栈配置
# =============================================================================

# Ingress Controller类型
# 支持值: "nginx", "traefik"
# 注意: 必须与集群中实际部署的Ingress Controller匹配
INGRESS_CLASS="traefik"

# 证书颁发者配置 (根据CERT_METHOD自动生成)
# 此值由脚本根据CERT_METHOD自动设置，无需手动配置
CERT_ISSUER=""

# =============================================================================
# SSL证书配置 (3种申请方式)
# =============================================================================

# 证书申请方式选择 (必选其一)
# 选项1: "letsencrypt-http01"     - HTTP验证申请生产证书
# 选项2: "letsencrypt-dns01"      - DNS验证申请生产证书
# 选项3: "letsencrypt-dns01-staging" - DNS验证申请测试证书
CERT_METHOD="letsencrypt-http01"

# DNS验证配置 (当CERT_METHOD包含"dns01"时需要)
# 支持的DNS提供商: cloudflare, route53, digitalocean, godaddy
DNS_PROVIDER="cloudflare"
DNS_API_TOKEN="your-dns-api-token"

# ⚠️ 隐私保护: 使用虚拟邮箱，不暴露真实邮箱给第三方
# 建议使用: noreply@${MAIN_DOMAIN} 或 ssl@${MAIN_DOMAIN}
CERT_EMAIL="noreply@${MAIN_DOMAIN}"

# =============================================================================
# 网络配置
# =============================================================================

# HTTPS端口 (通常为443，Kind环境可能需要自定义)
HTTPS_PORT="443"

# Matrix联邦端口 (通过.well-known委托，通常不需要直接暴露)
FEDERATION_PORT="8448"

# RTC服务端口 (NodePort配置)
RTC_UDP_PORT="30882"
RTC_TCP_PORT="30881"

# =============================================================================
# 部署环境配置
# =============================================================================

# Kubernetes命名空间
NAMESPACE="matrix"

# Helm Release名称
RELEASE_NAME="matrix-stack"

# ESS-HELM Chart版本
ESS_HELM_VERSION="25.6.2"

# Chart仓库地址
CHART_REPO="oci://ghcr.io/element-hq/ess-helm/matrix-stack"

# =============================================================================
# 测试环境配置示例 (仅供参考)
# =============================================================================

# 测试域名配置
# MAIN_DOMAIN="niub.one"
# ELEMENT_SUBDOMAIN="element"
# MATRIX_SUBDOMAIN="matrix"
# AUTH_SUBDOMAIN="mas"
# RTC_SUBDOMAIN="rtc"

# 测试技术栈
# INGRESS_CLASS="traefik"
# CERT_METHOD="letsencrypt-dns01"
# DNS_PROVIDER="cloudflare"
# DNS_API_TOKEN="****************************************"
# CERT_EMAIL="<EMAIL>"

# 测试端口配置
# HTTPS_PORT="3443"
# FEDERATION_PORT="3448"

# =============================================================================
# 高级配置 (可选)
# =============================================================================

# 资源限制配置
SYNAPSE_MEMORY_REQUEST="1Gi"
SYNAPSE_MEMORY_LIMIT="2Gi"
SYNAPSE_CPU_REQUEST="500m"
SYNAPSE_CPU_LIMIT="1000m"

# 存储配置
POSTGRES_STORAGE_SIZE="20Gi"
SYNAPSE_MEDIA_STORAGE_SIZE="50Gi"
STORAGE_CLASS="default"

# 日志级别
LOG_LEVEL="INFO"

# 备份配置
BACKUP_ENABLED="false"
BACKUP_SCHEDULE="0 2 * * *"  # 每天凌晨2点

# =============================================================================
# 使用说明
# =============================================================================

# 1. 复制此文件并重命名为 config.env
# 2. 根据实际环境修改上述变量值
# 3. 使用脚本加载变量: source config.env
# 4. 使用envsubst替换模板文件中的变量:
#    envsubst < templates/values-template.yaml > values.yaml
# 5. 验证配置: helm --dry-run upgrade --install ...
# 6. 执行部署: helm upgrade --install ...
