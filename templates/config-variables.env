# ESS-HELM Matrix服务配置变量定义文件
# 版本: 25.6.2
# 用途: 定义所有可配置的变量，支持不同环境的部署

# =============================================================================
# 域名配置 (必须配置)
# =============================================================================

# 主域名 - Matrix服务器的主要标识
# 影响: 用户ID格式 @username:${MAIN_DOMAIN}
# 示例: example.com, matrix.org, your-domain.com
MAIN_DOMAIN="example.com"

# 子域名前缀配置
# Element Web客户端访问地址: ${ELEMENT_SUBDOMAIN}.${MAIN_DOMAIN}
ELEMENT_SUBDOMAIN="element"

# Matrix服务器访问地址: ${MATRIX_SUBDOMAIN}.${MAIN_DOMAIN}
MATRIX_SUBDOMAIN="matrix"

# 认证服务访问地址: ${AUTH_SUBDOMAIN}.${MAIN_DOMAIN}
AUTH_SUBDOMAIN="mas"

# RTC服务访问地址: ${RTC_SUBDOMAIN}.${MAIN_DOMAIN}
RTC_SUBDOMAIN="rtc"

# =============================================================================
# 技术栈配置
# =============================================================================

# Ingress Controller类型
# 支持值: "nginx", "traefik"
# 注意: 必须与集群中实际部署的Ingress Controller匹配
INGRESS_CLASS="traefik"

# 证书颁发者配置
# 支持值: "letsencrypt-http01", "letsencrypt-cloudflare", "custom-issuer"
CERT_ISSUER="letsencrypt-cloudflare"

# =============================================================================
# SSL证书配置 (根据CERT_ISSUER选择对应配置)
# =============================================================================

# Cloudflare API配置 (当CERT_ISSUER="letsencrypt-cloudflare"时需要)
CLOUDFLARE_API_TOKEN="your-cloudflare-api-token"
CLOUDFLARE_EMAIL="<EMAIL>"

# Let's Encrypt配置
CERT_EMAIL="admin@${MAIN_DOMAIN}"
ACME_SERVER="https://acme-v02.api.letsencrypt.org/directory"  # 生产环境
# ACME_SERVER="https://acme-staging-v02.api.letsencrypt.org/directory"  # 测试环境

# =============================================================================
# 网络配置
# =============================================================================

# HTTPS端口 (通常为443，Kind环境可能需要自定义)
HTTPS_PORT="443"

# Matrix联邦端口 (通过.well-known委托，通常不需要直接暴露)
FEDERATION_PORT="8448"

# RTC服务端口 (NodePort配置)
RTC_UDP_PORT="30882"
RTC_TCP_PORT="30881"

# =============================================================================
# 部署环境配置
# =============================================================================

# Kubernetes命名空间
NAMESPACE="matrix"

# Helm Release名称
RELEASE_NAME="matrix-stack"

# ESS-HELM Chart版本
ESS_HELM_VERSION="25.6.2"

# Chart仓库地址
CHART_REPO="oci://ghcr.io/element-hq/ess-helm/matrix-stack"

# =============================================================================
# 测试环境配置示例 (仅供参考)
# =============================================================================

# 测试域名配置
# MAIN_DOMAIN="niub.one"
# ELEMENT_SUBDOMAIN="element"
# MATRIX_SUBDOMAIN="matrix"
# AUTH_SUBDOMAIN="mas"
# RTC_SUBDOMAIN="rtc"

# 测试技术栈
# INGRESS_CLASS="traefik"
# CERT_ISSUER="letsencrypt-cloudflare"
# CLOUDFLARE_API_TOKEN="****************************************"
# CERT_EMAIL="<EMAIL>"

# 测试端口配置
# HTTPS_PORT="3443"
# FEDERATION_PORT="3448"

# =============================================================================
# 高级配置 (可选)
# =============================================================================

# 资源限制配置
SYNAPSE_MEMORY_REQUEST="1Gi"
SYNAPSE_MEMORY_LIMIT="2Gi"
SYNAPSE_CPU_REQUEST="500m"
SYNAPSE_CPU_LIMIT="1000m"

# 存储配置
POSTGRES_STORAGE_SIZE="20Gi"
SYNAPSE_MEDIA_STORAGE_SIZE="50Gi"
STORAGE_CLASS="default"

# 日志级别
LOG_LEVEL="INFO"

# 备份配置
BACKUP_ENABLED="false"
BACKUP_SCHEDULE="0 2 * * *"  # 每天凌晨2点

# =============================================================================
# 使用说明
# =============================================================================

# 1. 复制此文件并重命名为 config.env
# 2. 根据实际环境修改上述变量值
# 3. 使用脚本加载变量: source config.env
# 4. 使用envsubst替换模板文件中的变量:
#    envsubst < templates/values-template.yaml > values.yaml
# 5. 验证配置: helm --dry-run upgrade --install ...
# 6. 执行部署: helm upgrade --install ...
