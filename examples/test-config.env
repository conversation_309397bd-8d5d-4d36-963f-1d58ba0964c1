# ESS-HELM Matrix服务测试配置
# 基于实际部署验证的配置参数
# 测试环境: 10.0.0.252 (jw/test123)
# 验证状态: ✅ 已通过部署测试

# =============================================================================
# 域名配置 (测试环境)
# =============================================================================

# ⚠️ 注意: niub.one仅为测试域名，实际部署时请替换为您的域名
MAIN_DOMAIN="niub.one"

# 子域名前缀 (已验证可用)
ELEMENT_SUBDOMAIN="element"    # element.niub.one
MATRIX_SUBDOMAIN="matrix"      # matrix.niub.one
AUTH_SUBDOMAIN="mas"           # mas.niub.one
RTC_SUBDOMAIN="rtc"            # rtc.niub.one

# =============================================================================
# 技术栈配置 (基于实际测试环境)
# =============================================================================

# Ingress Controller (测试环境实际使用)
INGRESS_CLASS="traefik"

# 证书管理 (已验证可用)
CERT_ISSUER="letsencrypt-cloudflare"

# =============================================================================
# SSL证书配置 (测试环境)
# =============================================================================

# Cloudflare API配置 (测试有效)
CLOUDFLARE_API_TOKEN="****************************************"
CLOUDFLARE_EMAIL="<EMAIL>"

# Let's Encrypt配置
CERT_EMAIL="<EMAIL>"
ACME_SERVER="https://acme-v02.api.letsencrypt.org/directory"

# =============================================================================
# 网络配置 (测试环境)
# =============================================================================

# HTTPS端口 (测试环境映射)
HTTPS_PORT="3443"

# Matrix联邦端口
FEDERATION_PORT="3448"

# RTC服务端口 (NodePort)
RTC_UDP_PORT="30882"
RTC_TCP_PORT="30881"

# =============================================================================
# 部署环境配置
# =============================================================================

# Kubernetes配置
NAMESPACE="matrix"
RELEASE_NAME="matrix-stack"

# ESS-HELM版本 (已验证)
ESS_HELM_VERSION="25.6.2"
CHART_REPO="oci://ghcr.io/element-hq/ess-helm/matrix-stack"

# =============================================================================
# 资源配置 (基于测试环境调优)
# =============================================================================

# Synapse资源限制
SYNAPSE_MEMORY_REQUEST="1Gi"
SYNAPSE_MEMORY_LIMIT="2Gi"
SYNAPSE_CPU_REQUEST="500m"
SYNAPSE_CPU_LIMIT="1000m"

# 存储配置
POSTGRES_STORAGE_SIZE="20Gi"
SYNAPSE_MEDIA_STORAGE_SIZE="50Gi"
STORAGE_CLASS="default"

# 日志配置
LOG_LEVEL="INFO"

# =============================================================================
# 测试验证信息
# =============================================================================

# 部署验证命令
# kubectl get pods -n matrix
# kubectl get svc -n matrix  
# kubectl get ingress -n matrix
# kubectl get secrets -n matrix | grep tls

# 访问地址验证
# Element Web: https://element.niub.one:3443
# Matrix服务器: https://matrix.niub.one:3443
# 认证服务: https://mas.niub.one:3443
# RTC服务: https://rtc.niub.one:3443

# 用户创建命令
# kubectl exec -n matrix -it deployment/matrix-stack-matrix-authentication-service -- mas-cli manage register-user

# 联邦测试
# https://federationtester.matrix.org/#niub.one

# =============================================================================
# 重要提醒
# =============================================================================

# 1. serverName配置
#    ✅ 正确: serverName: "niub.one" → 用户ID: @admin:niub.one
#    ❌ 错误: serverName: "matrix.niub.one" → 用户ID: @admin:matrix.niub.one

# 2. Ingress Controller匹配
#    测试环境只有Traefik，必须使用 INGRESS_CLASS="traefik"
#    如果使用nginx会导致webhook验证失败

# 3. 证书管理
#    使用Cloudflare API验证，需要有效的API Token
#    DNS记录必须指向正确的IP地址

# 4. 端口映射
#    Kind环境需要自定义端口映射
#    生产环境通常使用标准的80/443端口
