# ESS-HELM Matrix服务测试配置文件
# 版本: 25.6.2 (基于Element官方ESS-HELM稳定版)
# 测试环境: 10.0.0.252 (jw/test123)
# 验证状态: ✅ 已通过官方Schema验证和实际部署测试
# 
# ⚠️ 重要: 此为测试配置，实际部署时请替换域名和相关参数

# Matrix服务器名称配置
# ⚠️ 关键: 必须使用主域名，不是子域名
# 影响用户ID格式: @username:niub.one (正确)
# 错误示例: @username:matrix.niub.one (错误)
serverName: "niub.one"

# 全局Ingress配置
ingress:
  className: "traefik"  # 基于测试环境实际的Ingress Controller
  tlsEnabled: true
  annotations:
    # Traefik特定配置 (测试环境验证有效)
    traefik.ingress.kubernetes.io/router.tls: "true"

# Element Web客户端配置
elementWeb:
  enabled: true
  ingress:
    host: "element.niub.one"

# Synapse Matrix服务器配置
synapse:
  enabled: true
  ingress:
    host: "matrix.niub.one"

# Matrix认证服务配置
matrixAuthenticationService:
  enabled: true
  ingress:
    host: "mas.niub.one"

# Matrix RTC服务配置
matrixRTC:
  enabled: true
  ingress:
    host: "rtc.niub.one"

# Well-known委托配置
# ⚠️ 注意: 此组件不支持 ingress.host 属性
wellKnownDelegation:
  enabled: true

# 证书管理配置
certManager:
  clusterIssuer: "letsencrypt-cloudflare"

# =============================================================================
# 以下为可选的高级配置，基于测试环境优化
# =============================================================================

# Synapse资源配置 (可选)
# synapse:
#   resources:
#     requests:
#       memory: "1Gi"
#       cpu: "500m"
#     limits:
#       memory: "2Gi"
#       cpu: "1000m"

# PostgreSQL存储配置 (可选)
# postgres:
#   persistence:
#     size: "20Gi"
#     storageClass: "default"

# Element Web自定义配置 (可选)
# elementWeb:
#   additional:
#     default_theme: "dark"
#     show_labs_settings: true

# =============================================================================
# 部署验证信息
# =============================================================================

# 部署命令:
# helm upgrade --install matrix-stack oci://ghcr.io/element-hq/ess-helm/matrix-stack \
#   --version 25.6.2 --namespace matrix --values values-test.yaml

# 验证命令:
# kubectl get pods -n matrix
# kubectl get svc -n matrix
# kubectl get ingress -n matrix
# kubectl get secrets -n matrix | grep tls

# 访问地址 (测试环境):
# Element Web: https://element.niub.one:3443
# Matrix服务器: https://matrix.niub.one:3443  
# 认证服务: https://mas.niub.one:3443
# RTC服务: https://rtc.niub.one:3443

# 创建用户:
# kubectl exec -n matrix -it deployment/matrix-stack-matrix-authentication-service -- mas-cli manage register-user

# 联邦测试:
# https://federationtester.matrix.org/#niub.one
