# ESS-HELM 部署指南

## 📋 概述

本指南详细介绍如何使用ESS-HELM一键部署系统部署Matrix通信服务。该系统基于官方ESS-HELM 25.6.2稳定版，支持Router WAN IP自动检测、虚拟公网IP路由高可用和增强管理功能。

**版本**: v2.1
**基于**: ESS-HELM 25.6.2 官方稳定版 (OCI格式)
**OCI支持**: 完全符合Element官方OCI规范
**更新时间**: 2025-06-20

## 🎯 部署模式

### 1. 外部服务器部署
- **适用场景**: 需要公网访问的环境
- **核心功能**: Router WAN IP自动检测 + 虚拟公网IP路由
- **网络要求**: 公网IP地址，支持端口转发

### 2. 内部服务器部署
- **适用场景**: 内网环境，企业内部使用
- **核心功能**: 简化网络配置，增强安全性
- **网络要求**: 内网访问即可

## 🛠️ 系统要求

### 硬件要求
- **CPU**: 最少2核，推荐4核
- **内存**: 最少4GB，推荐8GB
- **存储**: 最少50GB可用空间
- **网络**: 稳定的网络连接

### 软件要求
- **操作系统**: Linux (Ubuntu 20.04+, CentOS 8+) 或 macOS
- **Kubernetes**: 版本 >= 1.20
- **Helm**: 版本 >= 3.8 (OCI支持要求)
- **Docker**: 版本 >= 20.10
- **kubectl**: 与Kubernetes集群版本兼容
- **网络访问**: 能够访问ghcr.io (Element官方OCI仓库)

### 网络要求
- **外部部署**: 公网IP地址，开放端口8443、8448、3478、5349
- **内部部署**: 内网访问，可选开放联邦端口8448

## 🚀 快速开始

### 方法一: 一键部署 (推荐)

```bash
# 下载并运行一键部署脚本
bash <(curl -sSL https://raw.githubusercontent.com/your-repo/ess-helm-deployment-package/main/setup.sh)
```

### 方法二: 手动部署

```bash
# 1. 下载部署包
git clone https://github.com/your-repo/ess-helm-deployment-package.git
cd ess-helm-deployment-package

# 2. 运行主入口脚本
chmod +x setup.sh
./setup.sh
```

## 📝 详细部署步骤

### 步骤1: 环境准备

1. **检查系统要求**
   ```bash
   # 检查Kubernetes连接
   kubectl cluster-info

   # 检查Helm版本 (需要3.8+支持OCI)
   helm version

   # 检查Docker状态
   docker version

   # 验证OCI仓库访问
   helm show chart oci://ghcr.io/element-hq/ess-helm/matrix-stack --version 25.6.2
   ```

2. **配置kubectl**
   ```bash
   # 确保kubectl可以访问Kubernetes集群
   kubectl get nodes
   ```

### 步骤2: 运行部署脚本

1. **启动主入口脚本**
   ```bash
   ./setup.sh
   ```

2. **选择部署模式**
   - 选择 `1` 进行外部服务器部署
   - 选择 `2` 进行内部服务器部署

### 步骤3: 配置参数

系统会引导您配置以下参数：

#### 基本配置
- **主域名**: 您的域名 (例如: example.com)
- **子域名**: 各服务的子域名配置
  - Element Web: element (默认)
  - Matrix服务器: matrix (默认)
  - MAS认证服务: mas (默认)
  - RTC服务: rtc (默认)
  - TURN服务: turn (默认)

#### 端口配置
- **HTTPS端口**: 8443 (默认)
- **Matrix联邦端口**: 8448 (默认)
- **TURN端口**: 3478, 5349 (默认)

#### 存储配置
- **服务目录**: ~/matrix (默认)
- **存储大小**: 根据需求调整

#### SSL证书配置
- **Let's Encrypt**: 自动申请和续期 (推荐)
- **自定义证书**: 使用已有证书文件

#### Router配置 (仅外部部署)
- **Router IP**: RouterOS设备IP地址
- **用户名**: RouterOS管理员用户名
- **密码**: RouterOS管理员密码
- **WAN接口**: WAN接口名称 (默认: ether1)

### 步骤4: 执行部署

1. **确认配置**
   - 系统会显示配置摘要
   - 确认无误后继续部署

2. **自动部署过程**
   - 下载官方ESS-HELM Chart
   - 生成配置文件
   - 创建Kubernetes资源
   - 启动辅助服务
   - 验证部署状态

### 步骤5: 验证部署

1. **检查服务状态**
   ```bash
   # 使用管理脚本检查状态
   ./scripts/admin.sh
   # 选择 "5. 查看部署状态"
   ```

2. **访问服务**
   - Element Web: https://element.yourdomain.com:8443
   - Matrix服务器: https://matrix.yourdomain.com:8443
   - MAS认证服务: https://mas.yourdomain.com:8443

## 🔧 高级配置

### Router WAN IP检测配置

```yaml
# 编辑配置文件
vim ~/matrix/configs/values-router-wan-ip-detection.yaml

# 主要配置项
routerWanIpDetection:
  enabled: true
  router:
    host: "***********"
    username: "admin"
    password: "your-password"
    port: 8728
  detection:
    schedule: "*/5 * * * * *"  # 5秒检测间隔
    wanInterface: "ether1"
```

### 虚拟公网IP路由配置

```yaml
# 编辑配置文件
vim ~/matrix/configs/values-virtual-public-ip-routing.yaml

# 主要配置项
virtualPublicIpRouting:
  enabled: true
  livekit:
    virtualPublicIp: "**********"
    enabled: true
  turn:
    virtualPublicIp: "**********"
    enabled: true
```

### 自定义域名配置

```bash
# 修改主配置文件
vim ~/matrix/configs/values.yaml

# 更新域名配置
global:
  serverName: "matrix.yourdomain.com"
  domain: "yourdomain.com"
```

## 🛡️ 安全配置

### 防火墙配置

```bash
# Ubuntu/Debian
sudo ufw allow 8443/tcp
sudo ufw allow 8448/tcp
sudo ufw allow 3478/udp
sudo ufw allow 5349/tcp

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=8443/tcp
sudo firewall-cmd --permanent --add-port=8448/tcp
sudo firewall-cmd --permanent --add-port=3478/udp
sudo firewall-cmd --permanent --add-port=5349/tcp
sudo firewall-cmd --reload
```

### SSL证书管理

```bash
# 查看证书状态
openssl x509 -in ~/matrix/certs/tls.crt -text -noout

# 续期Let's Encrypt证书
certbot renew --dry-run
```

## 📊 监控和维护

### 日志查看

```bash
# 查看部署日志
tail -f ~/matrix/deployment.log

# 查看Router WAN IP检测日志
tail -f ~/matrix/router-wan-ip-detection.log

# 查看虚拟IP路由日志
tail -f ~/matrix/virtual-ip-routing.log
```

### 服务管理

```bash
# 使用管理脚本
./scripts/admin.sh

# 可用功能:
# 1. 用户管理
# 2. 服务控制
# 3. 注册控制
# 4. 运维管理
# 5. 系统状态
```

### 备份和恢复

```bash
# 数据库备份
kubectl exec -n matrix deployment/postgresql -- pg_dump -U synapse synapse > backup.sql

# 媒体文件备份
kubectl cp matrix/synapse-0:/data/media ./media-backup

# 配置文件备份
cp -r ~/matrix/configs ~/matrix/configs-backup-$(date +%Y%m%d)
```

## 🆘 故障排除

### 常见问题

1. **无法连接到Kubernetes集群**
   ```bash
   # 检查kubeconfig
   kubectl config view
   kubectl config current-context
   ```

2. **Helm部署失败**
   ```bash
   # 检查Helm状态
   helm list -n matrix
   helm status matrix-stack -n matrix
   ```

3. **服务无法访问**
   ```bash
   # 检查服务状态
   kubectl get pods -n matrix
   kubectl get services -n matrix
   kubectl get ingress -n matrix
   ```

4. **Router WAN IP检测失败**
   ```bash
   # 检查Router连接
   nc -zv *********** 8728
   
   # 查看检测日志
   tail -f ~/matrix/router-wan-ip-detection.log
   ```

### 获取支持

- **查看日志**: 检查相关日志文件
- **运行诊断**: 使用 `./scripts/admin.sh` 进行系统诊断
- **社区支持**: 访问官方文档和社区论坛

## 📚 相关文档

- [管理指南](./admin-guide.md) - 详细的管理和维护指南
- [故障排除指南](./troubleshooting.md) - 常见问题解决方案
- [官方ESS-HELM文档](https://github.com/element-hq/ess-helm) - 官方文档

---

**注意**: 本指南基于ESS-HELM 25.6.2官方稳定版编写，请确保使用最新版本的部署包。
