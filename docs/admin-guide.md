# ESS-HELM 管理指南

## 📋 概述

本指南详细介绍如何使用ESS-HELM管理系统进行日常管理和维护。该系统采用混合管理架构，结合Matrix Authentication Service (MAS)、Synapse Admin API和Kubernetes原生管理，提供完整的用户管理、服务控制、注册控制和运维管理功能。

**版本**: v1.1
**基于**: ESS-HELM 25.6.2 + MAS Admin API + Synapse Admin API
**更新时间**: 2025-06-20

## 🛠️ 管理工具架构

### 混合管理架构

ESS-HELM 25.6.2采用混合管理架构，包含以下组件：

#### Matrix Authentication Service (MAS)
- **用途**: 用户认证、会话管理、OAuth2认证
- **管理工具**: `mas-cli` 命令行工具
- **API**: MAS Admin API (REST-like API)

#### Synapse Admin API
- **用途**: Matrix服务器管理、房间管理、联邦管理
- **管理工具**: HTTP API调用
- **API**: Synapse Admin API (REST API)

#### Kubernetes原生管理
- **用途**: 服务控制、资源管理、监控
- **管理工具**: `kubectl`、`helm`、官方`matrix-tools`

#### Matrix-Tools (官方Go工具)
- **用途**: 配置渲染、密钥生成、Synapse到MAS迁移
- **功能**:
  - `render-config`: 配置文件模板渲染
  - `generate-secrets`: 自动生成密钥和证书
  - `syn2mas`: Synapse到MAS的用户迁移
  - `tcpwait`: TCP端口等待工具
  - `deployment-markers`: 部署标记管理

### 官方管理工具详解

```bash
# 1. MAS用户管理 (推荐)
kubectl exec -n ess -it deploy/ess-matrix-authentication-service -- mas-cli manage register-user

# 2. Synapse服务管理
curl -X GET "https://matrix.yourdomain.com/_synapse/admin/v1/server_version" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# 3. Kubernetes服务控制
kubectl get pods -n ess
helm status ess -n ess

# 4. Matrix-Tools使用示例
# 配置渲染
matrix-tools render-config -output /tmp/config.yaml config1.yaml config2.yaml

# 密钥生成
matrix-tools generate-secrets -secrets "secret-name:key-name:rand32" -labels "app=matrix"

# Synapse到MAS迁移
matrix-tools syn2mas -config /path/to/mas-config -synapse-config /path/to/synapse-config

# TCP端口等待
matrix-tools tcpwait -address "postgresql:5432"
```

### 管理功能分工

#### MAS负责：
1. **用户注册和管理** - 创建、删除、修改用户
2. **认证控制** - 登录、会话、OAuth2
3. **用户属性** - 用户信息、权限设置

#### Synapse Admin API负责：
4. **房间管理** - 房间创建、删除、设置
5. **服务器管理** - 配置、统计、联邦
6. **媒体管理** - 文件上传、存储清理

#### Kubernetes负责：
7. **服务控制** - 启动、停止、重启、扩缩容
8. **运维管理** - 备份、日志、监控
9. **系统状态** - 查看部署状态

## 👥 用户管理

### 创建用户 (推荐使用MAS)

```bash
# 方法1: 使用MAS CLI (推荐)
kubectl exec -n ess -it deploy/ess-matrix-authentication-service -- mas-cli manage register-user

# 交互式创建用户
# ✔ Username · alice
# ✔ Password · ********
# 用户将获得Matrix ID: @alice:yourdomain.com

# 方法2: 使用MAS Admin API
curl -X POST "https://account.yourdomain.com/api/admin/v1/users" \
  -H "Authorization: Bearer YOUR_MAS_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "type": "user",
      "attributes": {
        "username": "newuser",
        "can_request_admin": false
      }
    }
  }'

# 方法3: 使用Synapse Admin API (传统方式)
curl -X PUT "https://matrix.yourdomain.com/_synapse/admin/v2/users/@newuser:yourdomain.com" \
  -H "Authorization: Bearer YOUR_SYNAPSE_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "password": "secure_password",
    "displayname": "New User",
    "admin": false
  }'
```

### 用户管理操作

#### 查看用户列表
```bash
# 获取所有用户
curl -X GET "https://matrix.yourdomain.com/_synapse/admin/v2/users" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

#### 重置用户密码
```bash
# 重置密码
curl -X POST "https://matrix.yourdomain.com/_synapse/admin/v1/reset_password/@user:yourdomain.com" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"new_password": "new_secure_password"}'
```

#### 设置管理员权限
```bash
# 设为管理员
curl -X PUT "https://matrix.yourdomain.com/_synapse/admin/v1/users/@user:yourdomain.com/admin" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"admin": true}'
```

#### 停用用户
```bash
# 停用用户账户
curl -X POST "https://matrix.yourdomain.com/_synapse/admin/v1/deactivate/@user:yourdomain.com" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"erase": false}'
```

### 批量用户操作

```bash
# 批量创建用户脚本示例
#!/bin/bash
USERS=("alice" "bob" "charlie")
DOMAIN="yourdomain.com"
TOKEN="YOUR_ACCESS_TOKEN"

for user in "${USERS[@]}"; do
  curl -X PUT "https://matrix.$DOMAIN/_synapse/admin/v2/users/@$user:$DOMAIN" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d "{\"password\": \"temp_password_$user\", \"displayname\": \"$user\"}"
done
```

## 🔧 服务控制

### 服务状态查看

```bash
# 查看所有Pod状态
kubectl get pods -n matrix

# 查看特定服务状态
kubectl get pods -n matrix -l app.kubernetes.io/name=synapse

# 查看服务详情
kubectl describe pod -n matrix <pod-name>
```

### 服务重启

```bash
# 重启Synapse服务
kubectl rollout restart deployment/synapse -n matrix

# 重启Element Web
kubectl rollout restart deployment/element-web -n matrix

# 重启PostgreSQL
kubectl rollout restart statefulset/postgresql -n matrix
```

### 服务扩缩容

```bash
# 扩展Synapse副本数
kubectl scale deployment/synapse --replicas=3 -n matrix

# 缩减副本数
kubectl scale deployment/synapse --replicas=1 -n matrix

# 查看扩缩容状态
kubectl get deployment/synapse -n matrix
```

### 服务日志查看

```bash
# 查看Synapse日志
kubectl logs -f deployment/synapse -n matrix

# 查看最近100行日志
kubectl logs --tail=100 deployment/synapse -n matrix

# 查看特定时间段日志
kubectl logs --since=1h deployment/synapse -n matrix
```

## 📝 注册控制

### 注册开关控制

```bash
# 开启注册
curl -X PUT "https://matrix.yourdomain.com/_synapse/admin/v1/server_notices" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"enable_registration": true}'

# 关闭注册
curl -X PUT "https://matrix.yourdomain.com/_synapse/admin/v1/server_notices" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"enable_registration": false}'
```

### 注册令牌管理

#### 创建注册令牌
```bash
# 创建无限制令牌
curl -X POST "https://matrix.yourdomain.com/_synapse/admin/v1/registration_tokens/new" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}'

# 创建限制使用次数的令牌
curl -X POST "https://matrix.yourdomain.com/_synapse/admin/v1/registration_tokens/new" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"uses_allowed": 10}'

# 创建有过期时间的令牌
curl -X POST "https://matrix.yourdomain.com/_synapse/admin/v1/registration_tokens/new" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"expiry_time": 1735689600000}'
```

#### 查看注册令牌
```bash
# 获取所有令牌
curl -X GET "https://matrix.yourdomain.com/_synapse/admin/v1/registration_tokens" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# 获取特定令牌信息
curl -X GET "https://matrix.yourdomain.com/_synapse/admin/v1/registration_tokens/TOKEN_HERE" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

#### 删除注册令牌
```bash
# 删除令牌
curl -X DELETE "https://matrix.yourdomain.com/_synapse/admin/v1/registration_tokens/TOKEN_HERE" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 邀请机制

```bash
# 创建邀请链接
curl -X POST "https://matrix.yourdomain.com/_synapse/admin/v1/registration_tokens/new" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"uses_allowed": 1, "expiry_time": 1735689600000}'

# 邀请链接格式
# https://element.yourdomain.com/#/register?token=TOKEN_HERE
```

## 🛠️ 运维管理

### 数据库管理

#### 数据库备份
```bash
# 创建数据库备份
kubectl exec -n matrix deployment/postgresql -- pg_dump -U synapse synapse > backup-$(date +%Y%m%d-%H%M%S).sql

# 压缩备份文件
gzip backup-$(date +%Y%m%d-%H%M%S).sql
```

#### 数据库恢复
```bash
# 恢复数据库
kubectl exec -i -n matrix deployment/postgresql -- psql -U synapse synapse < backup.sql
```

#### 数据库维护
```bash
# 连接到数据库
kubectl exec -it -n matrix deployment/postgresql -- psql -U synapse synapse

# 查看数据库大小
SELECT pg_size_pretty(pg_database_size('synapse'));

# 清理旧数据 (谨慎操作)
# 删除30天前的事件
DELETE FROM events WHERE origin_server_ts < (extract(epoch from now()) - 30*24*3600) * 1000;
```

### 媒体文件管理

```bash
# 查看媒体存储使用情况
kubectl exec -n matrix deployment/synapse -- du -sh /data/media

# 备份媒体文件
kubectl cp matrix/synapse-0:/data/media ./media-backup-$(date +%Y%m%d)

# 清理旧媒体文件 (使用Synapse管理API)
curl -X POST "https://matrix.yourdomain.com/_synapse/admin/v1/media/delete" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"before_ts": 1640995200000}'  # 2022年1月1日之前
```

### 日志管理

```bash
# 查看各服务日志
kubectl logs -f deployment/synapse -n matrix
kubectl logs -f deployment/element-web -n matrix
kubectl logs -f deployment/postgresql -n matrix

# 导出日志到文件
kubectl logs deployment/synapse -n matrix > synapse-logs-$(date +%Y%m%d).log

# 清理旧日志 (配置日志轮换)
# 编辑 values.yaml 中的日志配置
logging:
  level: "INFO"
  retention: "7d"
  maxSize: "100MB"
```

### 监控和告警

#### 资源使用监控
```bash
# 查看节点资源使用
kubectl top nodes

# 查看Pod资源使用
kubectl top pods -n matrix

# 查看存储使用
kubectl get pvc -n matrix
```

#### 健康检查
```bash
# 检查服务健康状态
curl -f https://matrix.yourdomain.com/_matrix/client/versions

# 检查联邦连接
curl -f https://matrix.yourdomain.com:8448/_matrix/federation/v1/version

# 检查Element Web
curl -f https://element.yourdomain.com/
```

### 性能优化

#### 数据库优化
```bash
# 连接到PostgreSQL
kubectl exec -it -n matrix deployment/postgresql -- psql -U synapse synapse

# 分析查询性能
EXPLAIN ANALYZE SELECT * FROM events WHERE room_id = '!room:yourdomain.com' LIMIT 100;

# 重建索引
REINDEX DATABASE synapse;

# 更新统计信息
ANALYZE;
```

#### 缓存优化
```bash
# 编辑Synapse配置增加缓存
# 在 values.yaml 中添加:
synapse:
  config:
    caches:
      global_factor: 2.0
      per_cache_factors:
        get_users_who_share_room_with_user: 5.0
        get_rooms_for_user: 2.0
```

## 🔍 监控和诊断

### 系统监控

```bash
# 使用管理脚本进行健康检查
./scripts/admin.sh
# 选择 "4. 运维管理" -> "6. 系统健康检查"
```

### 性能指标

```bash
# 查看Synapse指标 (如果启用了Prometheus)
curl https://matrix.yourdomain.com/_synapse/metrics

# 查看数据库连接数
kubectl exec -n matrix deployment/postgresql -- psql -U synapse -c "SELECT count(*) FROM pg_stat_activity;"
```

### 故障诊断

```bash
# 检查Pod状态
kubectl get pods -n matrix -o wide

# 查看事件
kubectl get events -n matrix --sort-by='.lastTimestamp'

# 检查网络连接
kubectl exec -n matrix deployment/synapse -- nslookup postgresql
```

## 📊 报告和分析

### 用户统计
```bash
# 获取用户统计信息
curl -X GET "https://matrix.yourdomain.com/_synapse/admin/v2/users" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" | jq '.total'

# 获取活跃用户数
curl -X GET "https://matrix.yourdomain.com/_synapse/admin/v1/statistics/users/media" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 房间统计
```bash
# 获取房间列表
curl -X GET "https://matrix.yourdomain.com/_synapse/admin/v1/rooms" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# 获取房间详情
curl -X GET "https://matrix.yourdomain.com/_synapse/admin/v1/rooms/!room:yourdomain.com" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 🔐 安全管理

### 访问控制
```bash
# 查看管理员用户
curl -X GET "https://matrix.yourdomain.com/_synapse/admin/v2/users?admin=true" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# 撤销用户会话
curl -X POST "https://matrix.yourdomain.com/_synapse/admin/v1/whois/@user:yourdomain.com" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 审计日志
```bash
# 查看登录日志
kubectl logs deployment/synapse -n matrix | grep "Successful login"

# 查看管理操作日志
kubectl logs deployment/synapse -n matrix | grep "admin"
```

## 📚 最佳实践

### 定期维护任务

1. **每日任务**
   - 检查服务状态
   - 查看错误日志
   - 监控资源使用

2. **每周任务**
   - 数据库备份
   - 清理旧日志
   - 更新安全补丁

3. **每月任务**
   - 性能分析
   - 容量规划
   - 安全审计

### 自动化脚本

```bash
# 创建定期备份脚本
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d-%H%M%S)
kubectl exec -n matrix deployment/postgresql -- pg_dump -U synapse synapse > backup-$DATE.sql
gzip backup-$DATE.sql
find . -name "backup-*.sql.gz" -mtime +7 -delete
```

```bash
# 添加到crontab
crontab -e
# 每天凌晨2点执行备份
0 2 * * * /path/to/backup.sh
```

---

**注意**: 本指南基于ESS-HELM 25.6.2官方稳定版和Synapse Admin API编写，请确保API访问令牌的安全性。
