# ESS-HELM Matrix服务部署包

基于Element官方ESS-HELM 25.6.2稳定版的完整Matrix通信服务部署解决方案。

## 🎯 核心特性

- **🚀 一键部署**: 支持 `bash <(curl -sSL <URL>/setup.sh)` 完全自动化部署
- **🛠️ 模板化配置**: 使用变量替换，支持任意域名环境
- **🔒 隐私保护**: SSL证书申请不暴露真实邮箱给第三方
- **📋 官方规范**: 严格遵循ESS-HELM官方Schema和OCI标准
- **🌐 多环境支持**: 从测试到生产的完整配置模板
- **🔧 混合管理**: 结合MAS Admin API、Synapse Admin API和Kubernetes原生管理

## 📁 项目结构

```
ess/
├── README.md                        # 项目说明文档
├── requirement.md                   # 完整需求文档
├── setup.sh                         # 主入口脚本
├── templates/                       # 配置模板目录
│   ├── values-template.yaml         # Helm values配置模板
│   ├── config-variables.env         # 变量定义文件
│   └── generate-config.sh           # 配置生成脚本
├── examples/                        # 示例配置目录
│   ├── test-config.env              # 测试环境配置示例
│   └── values-test.yaml             # 测试环境实际配置
└── docs/                           # 文档目录
    ├── admin-guide.md               # 管理指南
    ├── deployment-guide.md          # 部署指南
    ├── ssl-certificate-guide.md     # SSL证书配置指南
    └── troubleshooting.md           # 故障排除指南
```

## 🚀 快速开始

### 一键部署 (推荐)

```bash
# 下载并运行一键部署脚本
bash <(curl -sSL https://raw.githubusercontent.com/your-repo/ess/main/setup.sh)
```

### 手动部署

```bash
# 1. 克隆仓库
git clone https://github.com/your-repo/ess.git
cd ess

# 2. 运行主入口脚本
chmod +x setup.sh
./setup.sh
```

## 📋 系统要求

### 软件要求
- **Kubernetes**: 版本 >= 1.20
- **Helm**: 版本 >= 3.8 (支持OCI)
- **kubectl**: 与Kubernetes集群版本兼容
- **envsubst**: 用于变量替换 (gettext包)

### 网络要求
- 能够访问 `ghcr.io` (Element官方OCI仓库)
- 如使用DNS验证，需要DNS提供商API访问权限

## 🛠️ 使用流程

### 1. 生成配置文件

选择以下方式之一：

**使用测试配置 (快速开始)**:
```bash
cp examples/values-test.yaml values.yaml
```

**自定义配置**:
```bash
# 复制配置模板
cp templates/config-variables.env config.env

# 编辑配置变量
vim config.env

# 生成配置文件
./templates/generate-config.sh
```

### 2. 验证配置

```bash
helm upgrade --install matrix-stack oci://ghcr.io/element-hq/ess-helm/matrix-stack \
  --version 25.6.2 --namespace matrix --values values.yaml --dry-run
```

### 3. 执行部署

```bash
# 创建命名空间
kubectl create namespace matrix

# 部署Matrix服务
helm upgrade --install matrix-stack oci://ghcr.io/element-hq/ess-helm/matrix-stack \
  --version 25.6.2 --namespace matrix --values values.yaml --timeout 20m --wait
```

### 4. 验证部署

```bash
# 检查Pod状态
kubectl get pods -n matrix

# 检查服务状态
kubectl get svc -n matrix

# 检查Ingress状态
kubectl get ingress -n matrix
```

## 🔐 SSL证书配置

支持3种证书申请方式：

### 1. HTTP验证申请生产证书
```bash
CERT_METHOD="letsencrypt-http01"
CERT_EMAIL="<EMAIL>"
```

### 2. DNS验证申请生产证书
```bash
CERT_METHOD="letsencrypt-dns01"
DNS_PROVIDER="cloudflare"
DNS_API_TOKEN="your-cloudflare-api-token"
CERT_EMAIL="<EMAIL>"
```

### 3. DNS验证申请测试证书
```bash
CERT_METHOD="letsencrypt-dns01-staging"
DNS_PROVIDER="cloudflare"
DNS_API_TOKEN="your-cloudflare-api-token"
CERT_EMAIL="<EMAIL>"
```

**隐私保护**: 所有证书申请都使用虚拟邮箱，不暴露真实邮箱给第三方。

## 📊 配置变量

### 域名配置
```bash
MAIN_DOMAIN="example.com"           # 主域名
ELEMENT_SUBDOMAIN="element"         # Element Web子域名
MATRIX_SUBDOMAIN="matrix"           # Matrix服务器子域名
AUTH_SUBDOMAIN="mas"                # 认证服务子域名
RTC_SUBDOMAIN="rtc"                 # RTC服务子域名
```

### 技术配置
```bash
INGRESS_CLASS="traefik"             # Ingress Controller类型
CERT_METHOD="letsencrypt-dns01"     # 证书申请方式
DNS_PROVIDER="cloudflare"           # DNS提供商
DNS_API_TOKEN="your-api-token"      # DNS API令牌
```

## 📚 文档

- **[需求文档](requirement.md)** - 完整的需求规范和技术标准
- **[部署指南](docs/deployment-guide.md)** - 详细的部署步骤和配置说明
- **[SSL证书指南](docs/ssl-certificate-guide.md)** - SSL证书配置详细说明
- **[管理指南](docs/admin-guide.md)** - 服务管理和维护指南
- **[故障排除](docs/troubleshooting.md)** - 常见问题解决方案

## 🎯 重要说明

### serverName配置
**⚠️ 重要**: `serverName`必须使用主域名，不是子域名
- ✅ 正确: `serverName: "example.com"` → 用户ID: `@admin:example.com`
- ❌ 错误: `serverName: "matrix.example.com"` → 用户ID: `@admin:matrix.example.com`

### OCI规范
- 使用官方OCI仓库: `oci://ghcr.io/element-hq/ess-helm/matrix-stack`
- 需要Helm 3.8+版本支持OCI
- 不再使用传统的`helm repo add`方式

## 🔧 故障排除

### 常见问题
1. **Schema验证失败**: 检查配置文件是否符合官方规范
2. **OCI访问失败**: 确认网络可以访问`ghcr.io`
3. **Helm版本不兼容**: 升级到Helm 3.8+
4. **证书申请失败**: 检查DNS API权限和域名解析

### 获取帮助
- 查看详细日志: `kubectl logs -n matrix <pod-name>`
- 检查事件: `kubectl get events -n matrix`
- 运行诊断: `./setup.sh` 选择相应的诊断选项

## 📄 许可证

本项目基于 Apache 2.0 许可证开源。

## 🙏 致谢

- **Element团队**: 感谢提供优秀的ESS-HELM项目
- **Matrix社区**: 感谢开源的Matrix协议和生态
- **Kubernetes社区**: 感谢强大的容器编排平台

---

**⚡ 快速开始**: `bash <(curl -sSL <URL>/setup.sh)`

**📖 完整文档**: [需求文档](requirement.md) | [部署指南](docs/deployment-guide.md) | [SSL证书指南](docs/ssl-certificate-guide.md)
