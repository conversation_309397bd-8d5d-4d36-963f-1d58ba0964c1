#!/bin/bash

# ESS-HELM Matrix服务部署脚本
# 版本: 25.6.2 (基于Element官方ESS-HELM稳定版)
# 功能: 模板化配置生成和一键部署
# 日期: 2025-06-20

set -euo pipefail

# 脚本配置 - 支持管道执行
if [[ -n "${BASH_SOURCE[0]:-}" && -f "${BASH_SOURCE[0]}" ]]; then
    # 正常文件执行模式
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    echo -e "\033[0;34m[信息]\033[0m 使用本地文件执行模式: $SCRIPT_DIR"
else
    # 管道执行模式 - 下载完整项目
    echo -e "\033[0;34m[信息]\033[0m 检测到管道执行模式，正在下载完整项目..."
    SCRIPT_DIR="$(mktemp -d)"

    # 下载并解压项目文件
    if curl -fsSL https://github.com/niublab/ess/archive/refs/heads/main.tar.gz | tar -xz -C "$SCRIPT_DIR" --strip-components=1 2>/dev/null; then
        echo -e "\033[0;34m[信息]\033[0m 项目文件下载完成: $SCRIPT_DIR"

        # 设置脚本文件执行权限
        find "$SCRIPT_DIR" -name "*.sh" -type f -exec chmod +x {} \; 2>/dev/null || true
    else
        echo "错误: 无法下载项目文件，请检查网络连接"
        exit 1
    fi

    # 设置清理函数
    cleanup_project_dir() {
        if [[ -d "$SCRIPT_DIR" && "$SCRIPT_DIR" =~ ^/tmp/ ]]; then
            rm -rf "$SCRIPT_DIR" 2>/dev/null || true
        fi
    }
    trap cleanup_project_dir EXIT
fi

# 项目配置
readonly PROJECT_NAME="ESS-HELM Matrix服务"
readonly VERSION="25.6.2"
readonly LOG_FILE="$(mktemp -t ess-helm-deployment-XXXXXX).log"

# 验证必要文件存在
validate_required_files() {
    echo -e "\033[0;34m[信息]\033[0m 验证必要文件..."

    local required_files=(
        "requirement.md"
        "templates/values-template.yaml"
        "templates/config-variables.env"
        "templates/generate-config.sh"
        "examples/test-config.env"
        "examples/values-test.yaml"
    )

    for file in "${required_files[@]}"; do
        if [[ ! -f "$SCRIPT_DIR/$file" ]]; then
            echo "错误: 缺少必要文件 $file"
            echo "当前目录: $SCRIPT_DIR"
            ls -la "$SCRIPT_DIR" 2>/dev/null || echo "无法列出目录内容"
            exit 1
        fi
    done

    # 设置生成脚本执行权限
    chmod +x "$SCRIPT_DIR/templates/generate-config.sh" 2>/dev/null || true

    echo -e "\033[0;32m[成功]\033[0m 所有必要文件验证完成"
}

# 执行文件验证
validate_required_files

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

info() {
    log "${BLUE}[信息]${NC} $1"
}

success() {
    log "${GREEN}[成功]${NC} $1"
}

warning() {
    log "${YELLOW}[警告]${NC} $1"
}

error() {
    log "${RED}[错误]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    clear
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                ESS-HELM Matrix服务部署工具                   ║"
    echo "║                                                              ║"
    echo "║  版本: ${VERSION} (Element官方稳定版)                         ║"
    echo "║  功能: 模板化配置 + 一键部署 + 多环境支持                      ║"
    echo "║  特色: 变量化配置 + 隐私保护 + 官方规范合规                    ║"
    echo "║                                                              ║"
    echo "║  🚀 快速部署 | 🛠️ 模板配置 | 🔒 隐私保护                     ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo
}

# 环境检测函数
check_environment() {
    info "正在检测系统环境..."

    # 检测必需工具
    local required_tools=("curl" "kubectl" "helm" "envsubst")
    local missing_tools=()

    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        else
            info "✓ $tool 已安装"
            # 检查Helm版本是否支持OCI
            if [[ "$tool" == "helm" ]]; then
                local helm_version=$(helm version --short --client 2>/dev/null | grep -oE 'v[0-9]+\.[0-9]+' | sed 's/v//' || echo "0.0")
                local major_version=$(echo "$helm_version" | cut -d. -f1)
                local minor_version=$(echo "$helm_version" | cut -d. -f2)

                if [[ $major_version -lt 3 ]] || [[ $major_version -eq 3 && $minor_version -lt 8 ]]; then
                    warning "Helm版本 $helm_version 不支持OCI，需要3.8+版本"
                    missing_tools+=("helm")
                else
                    info "✓ Helm版本 $helm_version 支持OCI规范"
                fi
            fi
        fi
    done

    if [ ${#missing_tools[@]} -ne 0 ]; then
        error "以下工具需要安装: ${missing_tools[*]}"
        echo "请安装缺失的工具后重新运行脚本"
        exit 1
    fi

    # 检查OCI仓库连通性
    info "检查Element官方OCI仓库连通性..."
    if curl -sSf https://ghcr.io &>/dev/null; then
        success "✓ 可以访问ghcr.io (Element官方OCI仓库)"
    else
        warning "⚠ 无法访问ghcr.io，部署时可能遇到网络问题"
    fi

    success "环境检测完成"
}

# 显示主菜单
show_main_menu() {
    while true; do
        show_welcome
        echo -e "${CYAN}请选择操作:${NC}"
        echo
        echo "1. 生成配置文件 (基于模板和变量)"
        echo "2. 部署Matrix服务 (使用生成的配置)"
        echo "3. 查看部署状态"
        echo "4. 查看帮助文档"
        echo "5. 查看示例配置"
        echo "0. 退出程序"
        echo
        echo -e "${YELLOW}请选择 (0-5):${NC}"
        read -r choice

        case "$choice" in
            1)
                generate_configuration
                ;;
            2)
                deploy_matrix_service
                ;;
            3)
                check_deployment_status
                ;;
            4)
                show_help_documentation
                ;;
            5)
                show_example_configurations
                ;;
            0)
                info "感谢使用ESS-HELM Matrix服务部署工具！"
                exit 0
                ;;
            *)
                error "无效选择，请重新选择"
                read -p "按回车键继续..."
                ;;
        esac
    done
}

# 生成配置文件
generate_configuration() {
    info "开始生成配置文件..."
    echo

    echo -e "${CYAN}配置生成选项:${NC}"
    echo "1. 使用测试配置 (niub.one域名)"
    echo "2. 自定义配置 (手动编辑变量文件)"
    echo "3. 使用配置生成脚本"
    echo "0. 返回主菜单"
    echo
    echo -e "${YELLOW}请选择 (0-3):${NC}"
    read -r config_choice

    case "$config_choice" in
        1)
            use_test_configuration
            ;;
        2)
            use_custom_configuration
            ;;
        3)
            use_configuration_generator
            ;;
        0)
            return
            ;;
        *)
            error "无效选择，请重新选择"
            generate_configuration
            ;;
    esac
}

# 使用测试配置
use_test_configuration() {
    info "使用测试配置生成values.yaml..."

    if [[ -f "$SCRIPT_DIR/examples/values-test.yaml" ]]; then
        cp "$SCRIPT_DIR/examples/values-test.yaml" "$SCRIPT_DIR/values.yaml"
        success "✓ 测试配置已复制到 values.yaml"
        echo
        echo -e "${YELLOW}测试配置信息:${NC}"
        echo "  主域名: niub.one"
        echo "  Element Web: element.niub.one"
        echo "  Matrix服务器: matrix.niub.one"
        echo "  认证服务: mas.niub.one"
        echo "  RTC服务: rtc.niub.one"
        echo "  证书方式: Cloudflare DNS验证"
        echo
        warning "⚠️ 这是测试配置，生产环境请使用自定义配置"
    else
        error "测试配置文件不存在"
    fi

    read -p "按回车键继续..."
}

# 使用自定义配置
use_custom_configuration() {
    info "准备自定义配置..."

    if [[ -f "$SCRIPT_DIR/templates/config-variables.env" ]]; then
        cp "$SCRIPT_DIR/templates/config-variables.env" "$SCRIPT_DIR/config.env"
        success "✓ 配置模板已复制到 config.env"
        echo
        echo -e "${YELLOW}请编辑 config.env 文件，修改以下变量:${NC}"
        echo "  MAIN_DOMAIN - 您的主域名"
        echo "  CERT_METHOD - 证书申请方式"
        echo "  DNS_PROVIDER - DNS提供商 (如使用DNS验证)"
        echo "  DNS_API_TOKEN - DNS API令牌"
        echo "  CERT_EMAIL - 证书申请邮箱"
        echo
        echo "编辑完成后，运行配置生成脚本:"
        echo "  ./templates/generate-config.sh"
    else
        error "配置模板文件不存在"
    fi

    read -p "按回车键继续..."
}

# 使用配置生成脚本
use_configuration_generator() {
    info "启动配置生成脚本..."

    if [[ -x "$SCRIPT_DIR/templates/generate-config.sh" ]]; then
        cd "$SCRIPT_DIR"
        ./templates/generate-config.sh
        cd - > /dev/null
    else
        error "配置生成脚本不存在或无执行权限"
    fi

    read -p "按回车键继续..."
}

# 部署Matrix服务
deploy_matrix_service() {
    info "开始部署Matrix服务..."

    # 检查是否存在配置文件
    if [[ ! -f "$SCRIPT_DIR/values.yaml" ]]; then
        error "未找到配置文件 values.yaml"
        echo "请先生成配置文件 (选项1)"
        read -p "按回车键继续..."
        return
    fi

    echo -e "${CYAN}部署选项:${NC}"
    echo "1. 验证配置 (dry-run)"
    echo "2. 执行部署"
    echo "0. 返回主菜单"
    echo
    echo -e "${YELLOW}请选择 (0-2):${NC}"
    read -r deploy_choice

    case "$deploy_choice" in
        1)
            validate_configuration
            ;;
        2)
            execute_deployment
            ;;
        0)
            return
            ;;
        *)
            error "无效选择，请重新选择"
            deploy_matrix_service
            ;;
    esac
}

# 验证配置
validate_configuration() {
    info "验证配置文件..."

    echo "执行命令:"
    echo "helm upgrade --install matrix-stack oci://ghcr.io/element-hq/ess-helm/matrix-stack \\"
    echo "  --version 25.6.2 --namespace matrix --values values.yaml --dry-run"
    echo

    if helm upgrade --install matrix-stack oci://ghcr.io/element-hq/ess-helm/matrix-stack \
        --version 25.6.2 --namespace matrix --values "$SCRIPT_DIR/values.yaml" --dry-run; then
        success "✓ 配置验证通过"
    else
        error "✗ 配置验证失败，请检查配置文件"
    fi

    read -p "按回车键继续..."
}

# 执行部署
execute_deployment() {
    info "执行Matrix服务部署..."

    # 创建命名空间
    kubectl create namespace matrix --dry-run=client -o yaml | kubectl apply -f -

    echo "执行命令:"
    echo "helm upgrade --install matrix-stack oci://ghcr.io/element-hq/ess-helm/matrix-stack \\"
    echo "  --version 25.6.2 --namespace matrix --values values.yaml --timeout 20m --wait"
    echo

    if helm upgrade --install matrix-stack oci://ghcr.io/element-hq/ess-helm/matrix-stack \
        --version 25.6.2 --namespace matrix --values "$SCRIPT_DIR/values.yaml" \
        --timeout 20m --wait; then
        success "✓ Matrix服务部署成功"
        echo
        echo -e "${GREEN}部署完成！${NC}"
        echo "请等待所有Pod启动完成，然后可以访问服务"
    else
        error "✗ Matrix服务部署失败"
        echo "请检查日志和配置文件"
    fi

    read -p "按回车键继续..."
}

# 检查部署状态
check_deployment_status() {
    info "检查Matrix服务部署状态..."

    echo -e "${CYAN}Kubernetes资源状态:${NC}"
    echo

    # 检查命名空间
    if kubectl get namespace matrix &>/dev/null; then
        success "✓ matrix命名空间存在"
    else
        warning "⚠ matrix命名空间不存在"
    fi

    # 检查Helm Release
    if helm list -n matrix | grep -q matrix-stack; then
        success "✓ matrix-stack Helm Release存在"
        echo
        echo "Helm Release信息:"
        helm list -n matrix
    else
        warning "⚠ matrix-stack Helm Release不存在"
    fi

    echo
    echo "Pod状态:"
    kubectl get pods -n matrix 2>/dev/null || echo "无法获取Pod状态"

    echo
    echo "Service状态:"
    kubectl get svc -n matrix 2>/dev/null || echo "无法获取Service状态"

    echo
    echo "Ingress状态:"
    kubectl get ingress -n matrix 2>/dev/null || echo "无法获取Ingress状态"

    read -p "按回车键继续..."
}

# 显示帮助文档
show_help_documentation() {
    clear
    echo -e "${CYAN}ESS-HELM Matrix服务部署工具帮助${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo
    echo -e "${YELLOW}功能说明:${NC}"
    echo "1. 生成配置文件 - 基于模板和变量生成Helm values配置"
    echo "2. 部署Matrix服务 - 使用官方ESS-HELM Chart部署完整服务栈"
    echo "3. 查看部署状态 - 检查Kubernetes资源和服务状态"
    echo "4. 查看帮助文档 - 显示本帮助信息"
    echo "5. 查看示例配置 - 显示测试配置和模板文件"
    echo
    echo -e "${YELLOW}技术规范:${NC}"
    echo "• 基于Element官方ESS-HELM 25.6.2稳定版"
    echo "• 使用OCI格式Chart仓库 (ghcr.io/element-hq/ess-helm)"
    echo "• 支持模板化配置和变量替换"
    echo "• 提供3种SSL证书申请方式"
    echo "• 完全隐私保护，不暴露真实邮箱"
    echo
    echo -e "${YELLOW}配置文件:${NC}"
    echo "• requirement.md - 完整需求文档"
    echo "• templates/values-template.yaml - Helm values模板"
    echo "• templates/config-variables.env - 变量定义文件"
    echo "• templates/generate-config.sh - 配置生成脚本"
    echo "• examples/test-config.env - 测试配置示例"
    echo "• docs/ssl-certificate-guide.md - SSL证书配置指南"
    echo
    echo -e "${YELLOW}部署命令示例:${NC}"
    echo "helm upgrade --install matrix-stack oci://ghcr.io/element-hq/ess-helm/matrix-stack \\"
    echo "  --version 25.6.2 --namespace matrix --values values.yaml"
    echo
    read -p "按回车键继续..."
}

# 显示示例配置
show_example_configurations() {
    clear
    echo -e "${CYAN}示例配置文件${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo
    echo -e "${YELLOW}可用的示例配置:${NC}"
    echo

    if [[ -f "$SCRIPT_DIR/examples/test-config.env" ]]; then
        echo "1. 测试环境配置 (examples/test-config.env)"
        echo "   - 域名: niub.one"
        echo "   - 证书: Cloudflare DNS验证"
        echo "   - 用途: 测试和验证"
        echo
    fi

    if [[ -f "$SCRIPT_DIR/examples/values-test.yaml" ]]; then
        echo "2. 测试环境Helm配置 (examples/values-test.yaml)"
        echo "   - 基于测试配置生成的实际values文件"
        echo "   - 已通过官方Schema验证"
        echo "   - 可直接用于部署"
        echo
    fi

    if [[ -f "$SCRIPT_DIR/templates/config-variables.env" ]]; then
        echo "3. 配置变量模板 (templates/config-variables.env)"
        echo "   - 包含所有可配置变量"
        echo "   - 详细的配置说明"
        echo "   - 支持任意域名"
        echo
    fi

    echo -e "${YELLOW}查看文件内容:${NC}"
    echo "cat examples/test-config.env"
    echo "cat examples/values-test.yaml"
    echo "cat templates/config-variables.env"
    echo
    read -p "按回车键继续..."
}

# 临时文件清理函数
cleanup_temp_files() {
    if [[ -f "$LOG_FILE" ]]; then
        info "部署日志已保存到: $LOG_FILE"
    fi
}

# 主程序入口
main() {
    # 设置退出时清理临时文件
    trap cleanup_temp_files EXIT

    info "ESS-HELM Matrix服务部署工具启动"
    info "部署日志文件: $LOG_FILE"

    # 检查是否以root权限运行
    if [[ $EUID -eq 0 ]]; then
        warning "不建议以root权限运行此脚本"
    fi

    # 环境检测
    check_environment

    # 显示主菜单
    show_main_menu
}

# 信号处理
trap 'error "脚本被中断"; exit 1' INT TERM

# 启动主程序
main "$@"