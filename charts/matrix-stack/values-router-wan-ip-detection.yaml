# Router WAN IP自动检测配置
# 版本: v1.0
# 基于: RouterOS API，5秒检测间隔
# 功能: 完全本地化WAN IP检测，摒弃外部HTTP服务依赖

routerWanIpDetection:
  enabled: true
  mode: "wan-ip-only"
  
  # RouterOS连接配置
  router:
    host: "${ROUTER_IP}"
    username: "${ROUTER_USERNAME}"
    password: "${ROUTER_PASSWORD}"
    port: 8728
    useSSL: true
    timeout: 10
    
  # 检测配置
  detection:
    schedule: "*/5 * * * * *"  # 5秒检测间隔
    wanInterface: "${WAN_INTERFACE}"
    enableMultiInterface: true
    supportedInterfaces:
      - "ether1"
      - "pppoe-out1"
      - "lte1"
      - "bridge1"
    
  # 验证配置
  validation:
    enableWanIpVerification: true
    disableExternalServices: true  # 完全本地化
    enableIpFormatValidation: true
    enablePrivateIpFiltering: true
    
  # 日志配置
  logging:
    level: "INFO"
    enableDebug: false
    logRotation:
      enabled: true
      maxSize: "10MB"
      maxFiles: 5
    
  # 通知配置
  notifications:
    enabled: true
    methods:
      webhook:
        enabled: false
        url: ""
      email:
        enabled: false
        smtp: ""
      log:
        enabled: true
        
  # 性能配置
  performance:
    connectionPooling: true
    maxConcurrentConnections: 5
    retryAttempts: 3
    retryDelay: "2s"
    
  # 安全配置
  security:
    enableConnectionEncryption: true
    validateCertificates: false  # RouterOS通常使用自签名证书
    enableRateLimiting: true
    maxRequestsPerMinute: 60
    
  # 故障处理配置
  failureHandling:
    enableAutoRetry: true
    maxRetryAttempts: 5
    backoffStrategy: "exponential"
    maxBackoffDelay: "60s"
    enableFailoverMode: true
    
  # 监控配置
  monitoring:
    enabled: true
    healthCheck:
      enabled: true
      interval: "30s"
      timeout: "10s"
    metrics:
      enabled: true
      exportPrometheus: false
      
  # 集成配置
  integration:
    virtualIpRouting:
      enabled: true
      notifyOnChange: true
    kubernetes:
      enabled: true
      updateConfigMaps: true
      namespace: "matrix"
      
# 相关服务配置
services:
  # Router WAN IP检测服务
  routerWanIpDetector:
    enabled: true
    image:
      repository: "alpine"
      tag: "latest"
      pullPolicy: "IfNotPresent"
    
    resources:
      requests:
        memory: "64Mi"
        cpu: "50m"
      limits:
        memory: "128Mi"
        cpu: "100m"
        
    # 环境变量
    env:
      - name: ROUTER_IP
        value: "${ROUTER_IP}"
      - name: ROUTER_USERNAME
        value: "${ROUTER_USERNAME}"
      - name: ROUTER_PASSWORD
        valueFrom:
          secretKeyRef:
            name: router-credentials
            key: password
      - name: WAN_INTERFACE
        value: "${WAN_INTERFACE}"
      - name: DETECTION_INTERVAL
        value: "5"
        
    # 存储配置
    persistence:
      enabled: true
      size: "1Gi"
      storageClass: ""
      accessMode: "ReadWriteOnce"
      
    # 网络配置
    networking:
      hostNetwork: false
      dnsPolicy: "ClusterFirst"
      
    # 安全上下文
    securityContext:
      runAsNonRoot: true
      runAsUser: 1000
      runAsGroup: 1000
      fsGroup: 1000
      
    # 服务账户
    serviceAccount:
      create: true
      name: "router-wan-ip-detector"
      annotations: {}
      
    # Pod配置
    podAnnotations: {}
    podLabels: {}
    
    # 节点选择
    nodeSelector: {}
    tolerations: []
    affinity: {}
    
    # 生命周期钩子
    lifecycle:
      preStop:
        exec:
          command:
            - "/bin/sh"
            - "-c"
            - "kill -TERM $(cat /tmp/detector.pid)"
            
    # 健康检查
    livenessProbe:
      exec:
        command:
          - "/bin/sh"
          - "-c"
          - "test -f /tmp/detector.pid && kill -0 $(cat /tmp/detector.pid)"
      initialDelaySeconds: 30
      periodSeconds: 30
      timeoutSeconds: 5
      failureThreshold: 3
      
    readinessProbe:
      exec:
        command:
          - "/bin/sh"
          - "-c"
          - "test -f /tmp/current-wan-ip.txt"
      initialDelaySeconds: 10
      periodSeconds: 10
      timeoutSeconds: 3
      failureThreshold: 3

# ConfigMap配置
configMaps:
  routerWanIpDetection:
    enabled: true
    data:
      config.yaml: |
        router:
          host: "${ROUTER_IP}"
          port: 8728
          useSSL: true
          timeout: 10
        detection:
          interval: 5
          interface: "${WAN_INTERFACE}"
        logging:
          level: "INFO"
          
# Secret配置
secrets:
  routerCredentials:
    enabled: true
    type: "Opaque"
    data:
      username: "${ROUTER_USERNAME}"
      password: "${ROUTER_PASSWORD}"
      
# RBAC配置
rbac:
  create: true
  rules:
    - apiGroups: [""]
      resources: ["configmaps"]
      verbs: ["get", "list", "watch", "create", "update", "patch"]
    - apiGroups: [""]
      resources: ["secrets"]
      verbs: ["get", "list", "watch"]
    - apiGroups: [""]
      resources: ["events"]
      verbs: ["create"]

# 网络策略
networkPolicy:
  enabled: true
  policyTypes:
    - Ingress
    - Egress
  egress:
    # 允许访问RouterOS API
    - to: []
      ports:
        - protocol: TCP
          port: 8728
    # 允许DNS查询
    - to: []
      ports:
        - protocol: UDP
          port: 53
        - protocol: TCP
          port: 53
  ingress:
    # 允许来自同一命名空间的访问
    - from:
        - namespaceSelector:
            matchLabels:
              name: "matrix"

# 服务监控
serviceMonitor:
  enabled: false
  interval: "30s"
  scrapeTimeout: "10s"
  labels: {}
  annotations: {}

# Pod监控
podMonitor:
  enabled: false
  interval: "30s"
  scrapeTimeout: "10s"
  labels: {}
  annotations: {}

# 告警规则
prometheusRule:
  enabled: false
  groups:
    - name: "router-wan-ip-detection"
      rules:
        - alert: "RouterWanIpDetectionDown"
          expr: 'up{job="router-wan-ip-detector"} == 0'
          for: "5m"
          labels:
            severity: "critical"
          annotations:
            summary: "Router WAN IP检测服务已停止"
            description: "Router WAN IP检测服务已停止超过5分钟"
            
        - alert: "WanIpChangeDetected"
          expr: 'increase(wan_ip_changes_total[5m]) > 0'
          for: "0m"
          labels:
            severity: "warning"
          annotations:
            summary: "检测到WAN IP变化"
            description: "在过去5分钟内检测到WAN IP地址变化"
