# 内部服务器专用配置
# 版本: v1.0
# 功能: 针对内网环境的优化配置
# 特点: 简化网络配置，增强安全性，优化性能

internalServer:
  enabled: true
  
  # 网络配置
  networking:
    publicAccess: false
    internalOnly: true
    
    # 允许的网络段
    allowedNetworks:
      - "***********/16"
      - "10.0.0.0/8"
      - "**********/12"
      - "*********/8"
    
    # 服务类型配置
    serviceType: "ClusterIP"
    
    # 负载均衡配置
    loadBalancer:
      enabled: false  # 内网环境不需要外部负载均衡
      type: "ClusterIP"
      
    # Ingress配置
    ingress:
      enabled: true
      className: "nginx"
      internalOnly: true
      
  # 安全配置
  security:
    enableNetworkPolicy: true
    restrictExternalAccess: true
    
    # 允许的端口
    allowedPorts:
      - 8443   # HTTPS
      - 8448   # Matrix联邦 (可选)
      - 5432   # PostgreSQL (内部)
      - 6379   # Redis (内部)
      
    # TLS配置
    tls:
      enabled: true
      mode: "selfsigned"  # 内网环境使用自签名证书
      generateCerts: true
      
    # 认证配置
    authentication:
      enableBasicAuth: false
      enableOAuth: true
      enableLDAP: false
      
  # 性能优化 (内网环境)
  performance:
    enableCaching: true
    cacheSize: "512Mi"
    connectionPooling: true
    maxConnections: 100
    
    # 资源优化
    resourceOptimization:
      enabled: true
      cpuOptimization: true
      memoryOptimization: true
      
    # 数据库优化
    database:
      connectionPool:
        minConnections: 5
        maxConnections: 20
        idleTimeout: "300s"
      queryOptimization: true
      indexOptimization: true
      
  # 资源限制 (内网环境适中配置)
  resources:
    # Synapse资源配置
    synapse:
      requests:
        memory: "512Mi"
        cpu: "250m"
      limits:
        memory: "2Gi"
        cpu: "1000m"
        
    # Element Web资源配置
    elementWeb:
      requests:
        memory: "128Mi"
        cpu: "100m"
      limits:
        memory: "256Mi"
        cpu: "200m"
        
    # PostgreSQL资源配置
    postgresql:
      requests:
        memory: "256Mi"
        cpu: "200m"
      limits:
        memory: "1Gi"
        cpu: "500m"
        
    # Redis资源配置
    redis:
      requests:
        memory: "64Mi"
        cpu: "50m"
      limits:
        memory: "128Mi"
        cpu: "100m"
    
  # 监控配置
  monitoring:
    enabled: true
    internal: true
    
    # 指标收集
    metrics:
      enabled: true
      interval: "30s"
      retention: "7d"
      
    # 日志配置
    logging:
      level: "INFO"
      retention: "7d"
      maxSize: "100MB"
      
    # 健康检查
    healthCheck:
      enabled: true
      interval: "30s"
      timeout: "10s"
      
  # 高可用配置 (内网环境简化)
  highAvailability:
    enabled: false  # 内网环境通常不需要复杂的高可用
    replication:
      enabled: false
    clustering:
      enabled: false
      
  # 备份配置
  backup:
    enabled: true
    schedule: "0 2 * * *"  # 每天凌晨2点
    retention: "7d"
    
    # 备份存储
    storage:
      type: "local"
      path: "/backup"
      size: "10Gi"
      
    # 备份内容
    includes:
      - "database"
      - "media"
      - "config"
      
  # 存储配置
  storage:
    class: "local-path"  # 适用于单节点或小型集群
    
    # 各服务存储配置
    synapse:
      size: "20Gi"
      accessMode: "ReadWriteOnce"
      
    postgresql:
      size: "10Gi"
      accessMode: "ReadWriteOnce"
      
    redis:
      size: "1Gi"
      accessMode: "ReadWriteOnce"
      
    media:
      size: "50Gi"
      accessMode: "ReadWriteOnce"

# 网络策略配置
networkPolicy:
  enabled: true
  
  # 策略类型
  policyTypes:
    - Ingress
    - Egress
  
  # 入站规则
  ingress:
    # 允许来自同一命名空间的访问
    - from:
        - namespaceSelector:
            matchLabels:
              name: "matrix"
        - podSelector: {}
      ports:
        - protocol: TCP
          port: 8443
        - protocol: TCP
          port: 8448
          
    # 允许来自内网的访问
    - from:
        - ipBlock:
            cidr: "***********/16"
        - ipBlock:
            cidr: "10.0.0.0/8"
        - ipBlock:
            cidr: "**********/12"
      ports:
        - protocol: TCP
          port: 8443
        - protocol: TCP
          port: 8448
  
  # 出站规则
  egress:
    # 允许访问同一命名空间
    - to:
        - namespaceSelector:
            matchLabels:
              name: "matrix"
        - podSelector: {}
              
    # 允许访问kube-system (DNS等)
    - to:
        - namespaceSelector:
            matchLabels:
              name: "kube-system"
              
    # 允许访问内网
    - to:
        - ipBlock:
            cidr: "***********/16"
        - ipBlock:
            cidr: "10.0.0.0/8"
        - ipBlock:
            cidr: "**********/12"
            
    # 允许DNS查询
    - to: []
      ports:
        - protocol: TCP
          port: 53
        - protocol: UDP
          port: 53

# DNS配置
dns:
  enabled: true
  
  # 自定义域名
  customDomains:
    - "${MAIN_DOMAIN}"
    - "${ELEMENT_SUBDOMAIN}.${MAIN_DOMAIN}"
    - "${MATRIX_SUBDOMAIN}.${MAIN_DOMAIN}"
    - "${MAS_SUBDOMAIN}.${MAIN_DOMAIN}"
    - "${RTC_SUBDOMAIN}.${MAIN_DOMAIN}"
    - "${TURN_SUBDOMAIN}.${MAIN_DOMAIN}"
  
  # DNS服务器配置
  servers:
    - "*******"
    - "*******"
    - "*******"
    
  # DNS缓存配置
  cache:
    enabled: true
    ttl: "300s"
    maxEntries: 1000

# TURN服务配置 (内网优化)
turn:
  enabled: true
  
  # 基本配置
  config:
    realm: "${TURN_SUBDOMAIN}.${MAIN_DOMAIN}"
    listening-port: 3478
    tls-listening-port: 5349
    min-port: 30152
    max-port: 33152
    
    # 内网TURN配置
    external-ip: "auto-detect"
    relay-ip: "auto-detect"
    
    # 网络接口配置
    listening-ip: "0.0.0.0"
    relay-ip-range: "***********/16,10.0.0.0/8,**********/12"
    
    # 认证配置
    use-auth-secret: true
    static-auth-secret: "${TURN_AUTH_SECRET}"
    
    # 日志配置
    log-file: "/var/log/turn.log"
    verbose: false
    
  # 资源配置
  resources:
    requests:
      memory: "128Mi"
      cpu: "100m"
    limits:
      memory: "256Mi"
      cpu: "200m"
      
  # 网络配置
  networking:
    hostNetwork: false
    serviceType: "ClusterIP"

# 证书管理
certificates:
  # 自签名证书配置
  selfSigned:
    enabled: true
    
    # 证书信息
    subject:
      country: "CN"
      organization: "Matrix Internal"
      organizationalUnit: "IT Department"
      commonName: "${MATRIX_SUBDOMAIN}.${MAIN_DOMAIN}"
      
    # 扩展信息
    extensions:
      subjectAltNames:
        - "DNS:${ELEMENT_SUBDOMAIN}.${MAIN_DOMAIN}"
        - "DNS:${MATRIX_SUBDOMAIN}.${MAIN_DOMAIN}"
        - "DNS:${MAS_SUBDOMAIN}.${MAIN_DOMAIN}"
        - "DNS:${RTC_SUBDOMAIN}.${MAIN_DOMAIN}"
        - "DNS:${TURN_SUBDOMAIN}.${MAIN_DOMAIN}"
        
    # 有效期
    validity:
      days: 365
      
  # 证书轮换
  rotation:
    enabled: true
    beforeExpiry: "30d"
    
# 服务发现
serviceDiscovery:
  enabled: true
  
  # 内部服务发现
  internal:
    enabled: true
    namespace: "matrix"
    
  # 外部服务发现
  external:
    enabled: false  # 内网环境通常不需要

# 配置管理
configManagement:
  # 配置热重载
  hotReload:
    enabled: true
    watchInterval: "30s"
    
  # 配置验证
  validation:
    enabled: true
    strictMode: false
    
  # 配置备份
  backup:
    enabled: true
    schedule: "0 1 * * *"
    retention: "30d"
